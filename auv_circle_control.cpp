/**
 * AUV Circle Motion Control Library
 * Circle motion around pool with 1m radius
 * Date: 2025.7.21
 */

#define AUV_CIRCLE_CONTROL_EXPORTS
#include "auv_circle_control.h"

#include <cmath>
#include <cstring>
#include <chrono>

// AUV Controller internal structure
enum SearchState {
    SEARCH_FORWARD = 0,
    SEARCH_TURNING_LEFT = 1,
    SEARCH_TURNING_RIGHT = 2,
    SEARCH_SHORT_FORWARD = 3,
    SEARCH_STABILIZING = 4,
    SEARCH_COMPLETE = 5
};

struct AUVController {
    // Control parameters
    ControlParams params;
    
    // State variables
    bool initialized;
    bool is_active;
    double start_time;
    double elapsed_time;
    double initial_angle;
    double target_angle;
    double angle_error;
    double last_angle_error;
    double angle_integral;
    
    // Depth control variables
    double initial_depth;
    double depth_error;
    double last_depth_error;
    double depth_integral;
    
    // arch search variables
    SearchState search_state;
    int current_leg;           // current leg number
    double leg_start_time;     // current leg start time
    double turn_start_angle;   // turn start angle
    double target_turn_angle;  // target turn angle
    double long_leg_duration;  // long leg duration
    double short_leg_duration; // short leg duration
    bool turn_next;            // next is left or right turn
    bool current_cycle_turn_left;  // current cycle turn left
    int cycle_turn_count;          // current cycle turn count
    int max_cycles;           // max cycles
    bool search_completed;    // search completed
    
    // Add absolute angle tracking
    double reference_angle;        // reference starting angle
    double expected_total_turn;    // expected total turn angle
    
    double current_leg_target_angle; 
    bool need_update_target_angle;
    MotionState current_state;
    
    AUVController() : initialized(false), is_active(false), 
                     start_time(0.0), elapsed_time(0.0), initial_angle(0.0),
                     target_angle(0.0), angle_error(0.0), last_angle_error(0.0),
                     angle_integral(0.0), initial_depth(0.0), depth_error(0.0),
                     last_depth_error(0.0), depth_integral(0.0) {
        // Set default parameters
        params.radius = 1.0;
        params.target_speed = 0.3;
        params.max_angular_velocity = 0.5;
        params.target_depth = 1.0;
        params.depth_tolerance = 0.1;
        params.kp = 10;      
        params.ki = 0.2;    
        params.kd = 2;      
        params.kp_depth = 15;
        params.ki_depth = 0.3;
        params.kd_depth = 5;
        
        // arch search initialization
        search_state = SEARCH_FORWARD;
        current_leg = 0;
        leg_start_time = 0.0;
        turn_start_angle = 0.0;
        target_turn_angle = 0.0;
        long_leg_duration = 8.0;   // long leg 5 seconds
        short_leg_duration = 4.0;   // short leg 2 seconds
        turn_next = true;      // start left turn
        current_cycle_turn_left = true;  // first cycle left turn
        cycle_turn_count = 0;
        max_cycles = 3;          // 3 cycles
        search_completed = false;
        
        // Initialize new added members
        reference_angle = 0.0;
        expected_total_turn = 0.0;
        
        current_leg_target_angle = 0.0;
        need_update_target_angle = true;
        // Initialize motion state
        memset(&current_state, 0, sizeof(MotionState));
        current_state.motion_mode = MOTION_STOP;
        memset(&current_state.thruster_pwm, 0, sizeof(ThrusterPWM));
    }
};

// Utility functions
const double PI = 3.14159265358979323846;

double normalize_angle_deg(double angle) {
    while (angle > 180.0) angle -= 360.0;
    while (angle < -180.0) angle += 360.0;
    return angle;
}

double angle_difference_deg(double target, double current) {
    double diff = target - current;
    // ensure diff in [-180, 180], choose shortest path
    while (diff > 180.0) diff -= 360.0;
    while (diff < -180.0) diff += 360.0;
    return diff;
}

double get_current_time() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration<double>(duration).count();
}

double pid_controller(double error, double integral, double derivative, 
                     double kp, double ki, double kd) {
    return kp * error + ki * integral + kd * derivative;
}

int clamp_pwm(double value) {
    int pwm = static_cast<int>(value);
    if (pwm > 1900) return 1900;
    if (pwm < 1100) return 1100;
    return pwm;
}

double calculate_target_angle(double elapsed_time, double target_speed, double radius) {
    double angular_velocity_rad = target_speed / radius;
    double angular_velocity_deg = angular_velocity_rad * 180.0 / PI;
    return angular_velocity_deg * elapsed_time;
}

void calculate_thruster_pwm(double forward_thrust, double differential_thrust, 
                           double vertical_thrust, ThrusterPWM* pwm) {
    // horizontal: differential control
    double left_horizontal = forward_thrust + differential_thrust;
    double right_horizontal = forward_thrust - differential_thrust;
    
    // horizontal: 1100 forward, 1900 reverse
    pwm->horizontal_front_left = clamp_pwm(left_horizontal);
    pwm->horizontal_front_right = clamp_pwm(right_horizontal);
    pwm->horizontal_back_left = clamp_pwm(left_horizontal);
    pwm->horizontal_back_right = clamp_pwm(right_horizontal);
    
    // vertical
    double vertical_base = 1500 + vertical_thrust;
    pwm->vertical_front_left = clamp_pwm(vertical_base);
    pwm->vertical_front_right = clamp_pwm(3000 - vertical_base);
    pwm->vertical_back_left = clamp_pwm(3000 - vertical_base);
    pwm->vertical_back_right = clamp_pwm(vertical_base);
}

// API implementations
AUV_CIRCLE_API AUVControllerHandle auv_controller_create(void) {
    AUVController* controller = new AUVController();
    return static_cast<AUVControllerHandle>(controller);
}

AUV_CIRCLE_API void auv_controller_destroy(AUVControllerHandle handle) {
    if (handle) {
        AUVController* controller = static_cast<AUVController*>(handle);
        delete controller;
    }
}

AUV_CIRCLE_API AUVError auv_controller_init(
    AUVControllerHandle handle,
    const ControlParams* params
) {
    if (!handle || !params) {
        return AUV_ERROR_INVALID_PARAM;
    }
    
    if (params->radius <= 0 || params->target_speed <= 0 || 
        params->max_angular_velocity <= 0) {
        return AUV_ERROR_INVALID_PARAM;
    }
    
    AUVController* controller = static_cast<AUVController*>(handle);
    controller->params = *params;
    controller->initialized = true;
    controller->is_active = false;
    
    return AUV_SUCCESS;
}

AUV_CIRCLE_API AUVError auv_controller_update(
    AUVControllerHandle handle,
    double current_angle,
    double current_angular_velocity,
    double current_linear_velocity,
    double current_depth,
    MotionState* state
) {
    if (!handle || !state) {
        return AUV_ERROR_INVALID_PARAM;
    }
    
    AUVController* controller = static_cast<AUVController*>(handle);
    if (!controller->initialized) {
        return AUV_ERROR_NOT_INITIALIZED;
    }
    
    double current_time = get_current_time();
    if (controller->is_active) {
        controller->elapsed_time = current_time - controller->start_time;
    }
    
    controller->current_state.angle = current_angle;
    controller->current_state.angular_velocity = current_angular_velocity;
    controller->current_state.linear_velocity = current_linear_velocity;
    controller->current_state.is_active = controller->is_active ? 1 : 0;
    
    if (!controller->is_active) {
        controller->current_state.motion_mode = MOTION_STOP;
        memset(&controller->current_state.thruster_pwm, 0, sizeof(ThrusterPWM));
    } else {
        controller->current_state.motion_mode = MOTION_CIRCLE;
        
        // keep depth
        controller->depth_error = controller->params.target_depth - current_depth;
        double depth_derivative = (controller->depth_error - controller->last_depth_error) / 0.1;
        controller->depth_integral += controller->depth_error * 0.1;
        
        if (controller->depth_integral > 1.0) controller->depth_integral = 1.0;
        if (controller->depth_integral < -1.0) controller->depth_integral = -1.0;
        
        double depth_pid_output = pid_controller(
            controller->depth_error, controller->depth_integral, depth_derivative,
            controller->params.kp_depth, controller->params.ki_depth, controller->params.kd_depth
        );
        
        if (depth_pid_output > 200) depth_pid_output = 200;
        if (depth_pid_output < -200) depth_pid_output = -200;
        
        // circle search control
        double base_thrust = 0;
        double turn_thrust = 0;
        
        switch (controller->search_state) {
            case SEARCH_FORWARD: {
                if(controller->need_update_target_angle){
                    controller->current_leg_target_angle = current_angle;
                    controller->need_update_target_angle = false;
                    printf("long leg start , set target angle:%.1f\n", controller->current_leg_target_angle);
                }
                base_thrust = 1350;
                
                double heading_error = angle_difference_deg(controller->current_leg_target_angle, current_angle);
                
                const double deadzone_threshold = 5.0; 
                
                if (fabs(heading_error) > deadzone_threshold) {

                    double heading_derivative = (heading_error - controller->last_angle_error) / 0.1;
                    controller->angle_integral += heading_error * 0.1;
                    
                    if (controller->angle_integral > 10.0) controller->angle_integral = 10.0;
                    if (controller->angle_integral < -10.0) controller->angle_integral = -10.0;
                    
                    turn_thrust = pid_controller(heading_error, controller->angle_integral, heading_derivative,
                                               controller->params.kp, controller->params.ki, controller->params.kd);
                    
                    if (turn_thrust > 150) turn_thrust = 150;
                    if (turn_thrust < -150) turn_thrust = -150;
                    
                    printf("PID: error=%.1f°,output=%.1f\n", heading_error, turn_thrust);
                } else {
                    turn_thrust = 0;
                    controller->angle_integral = 0; 
                    printf("Deadzone: error=%.1f°, no adjustment\n", heading_error);
                }
                
                controller->last_angle_error = heading_error;
                if (controller->elapsed_time - controller->leg_start_time >= controller->long_leg_duration) {

                    controller->search_state = SEARCH_STABILIZING;
                    controller->leg_start_time = controller->elapsed_time; 
                    controller->need_update_target_angle = true;
                    printf("long leg complete, start stabilizing\n");
                }
                break;
            }
            case SEARCH_STABILIZING: {
                base_thrust = 1500;  
                turn_thrust = 0;     
                
                if (controller->elapsed_time - controller->leg_start_time >= 1.0) {  
                    if (controller->current_cycle_turn_left) {
                        controller->search_state = SEARCH_TURNING_LEFT;
                        controller->target_turn_angle = current_angle + 90.0;
                        printf("stabilizing complete, start left turn\n");
                    } else {
                        controller->search_state = SEARCH_TURNING_RIGHT;
                        controller->target_turn_angle = current_angle - 90.0;
                        printf("stabilizing complete, start right turn\n");
                    }
                    controller->target_turn_angle = normalize_angle_deg(controller->target_turn_angle);
                }
                break;
            }
            case SEARCH_TURNING_LEFT: {
                base_thrust = 1500;
                
                double left_angle_error = angle_difference_deg(controller->target_turn_angle, current_angle);
                
                if (fabs(left_angle_error) < 3.0) {
                    turn_thrust = 0;
                    printf("left turn completed! actual error: %.1f°\n", left_angle_error);
                    
                    if (controller->cycle_turn_count == 0) {
                        controller->search_state = SEARCH_SHORT_FORWARD;
                        controller->leg_start_time = controller->elapsed_time;
                        controller->cycle_turn_count = 1;
                        controller->need_update_target_angle = true;  // important,reset target angle
                        printf("first left turn completed, start short leg\n");
                    } else {
                        controller->cycle_turn_count = 0;
                        controller->current_leg++;
                        controller->current_cycle_turn_left = !controller->current_cycle_turn_left;
                        
                        if (controller->current_leg >= controller->max_cycles) {
                            controller->search_state = SEARCH_COMPLETE;
                            printf("search completed!\n");
                        } else {
                            controller->search_state = SEARCH_FORWARD;
                            controller->leg_start_time = controller->elapsed_time;
                            controller->need_update_target_angle = true;  // important,reset target angle
                            printf("cycle completed, start new cycle long leg\n");
                        }
                    }
                } else {
                    if(fabs(left_angle_error) > 45.0){
                        turn_thrust = (left_angle_error > 0) ? 100 : -100;
                    }else if(fabs(left_angle_error) > 15.0){
                        turn_thrust = (left_angle_error > 0) ? 80 : -80;
                    }else{
                        turn_thrust = (left_angle_error > 0) ? 50 : -50;
                    }
                    printf("left turn, still need turn %.1f°\n", left_angle_error);
                }
                break;
            }
            case SEARCH_TURNING_RIGHT: {
                base_thrust = 1500;
                
                double right_angle_error = angle_difference_deg(controller->target_turn_angle, current_angle);
                
                if (fabs(right_angle_error) < 3.0) {
                    turn_thrust = 0;
                    printf("right turn completed! actual error: %.1f°\n", right_angle_error);
                    
                    if (controller->cycle_turn_count == 0) {
                        controller->search_state = SEARCH_SHORT_FORWARD;
                        controller->leg_start_time = controller->elapsed_time;
                        controller->cycle_turn_count = 1;
                        controller->need_update_target_angle = true;  // important,reset target angle
                        printf("first right turn completed, start short leg\n");
                    } else {
                        controller->cycle_turn_count = 0;
                        controller->current_leg++;
                        controller->current_cycle_turn_left = !controller->current_cycle_turn_left;
                        
                        if (controller->current_leg >= controller->max_cycles) {
                            controller->search_state = SEARCH_COMPLETE;
                            printf("search completed!\n");
                        } else {
                            controller->search_state = SEARCH_FORWARD;
                            controller->leg_start_time = controller->elapsed_time;
                            controller->need_update_target_angle = true;  // important,reset target angle
                            printf("cycle completed, start new cycle long leg\n");
                        }
                    }
                } else {
                    if(fabs(right_angle_error) > 45.0){
                        turn_thrust = (right_angle_error > 0) ? 100 : -100;
                    }else if(fabs(right_angle_error) > 15.0){
                        turn_thrust = (right_angle_error > 0) ? 80 : -80;
                    }else{
                        turn_thrust = (right_angle_error > 0) ? 50 : -50;
                    }
                    printf("right turn, still need turn %.1f°\n", right_angle_error);
                }
                break;
            }
            case SEARCH_SHORT_FORWARD: {
                if(controller->need_update_target_angle){
                    controller->current_leg_target_angle = current_angle;
                    controller->need_update_target_angle = false;
                    printf("short leg start , set target angle:%.1f\n", controller->current_leg_target_angle);
                }
                base_thrust = 1350;
                
                double heading_error = angle_difference_deg(controller->current_leg_target_angle, current_angle);
                
                const double deadzone_threshold = 5.0;
                
                if (fabs(heading_error) > deadzone_threshold) {
                    double heading_derivative = (heading_error - controller->last_angle_error) / 0.1;
                    controller->angle_integral += heading_error * 0.1;
                    
                    if (controller->angle_integral > 10.0) controller->angle_integral = 10.0;
                    if (controller->angle_integral < -10.0) controller->angle_integral = -10.0;
                    
                    turn_thrust = pid_controller(heading_error, controller->angle_integral, heading_derivative,
                                               controller->params.kp, controller->params.ki, controller->params.kd);
                    
                    if (turn_thrust > 150) turn_thrust = 150;
                    if (turn_thrust < -150) turn_thrust = -150;
                } else {
                    turn_thrust = 0;
                    controller->angle_integral = 0;
                }
                
                controller->last_angle_error = heading_error;
                if (controller->elapsed_time - controller->leg_start_time >= controller->short_leg_duration) {
                    controller->search_state = SEARCH_STABILIZING;
                    controller->leg_start_time = controller->elapsed_time;
                    printf("short leg complete, start stabilizing\n");
                }
                break;
            }
            case SEARCH_COMPLETE: {
                base_thrust = 1500;
                turn_thrust = 0;
                break;
            }
            default: {
                base_thrust = 1500;
                turn_thrust = 0;
                break;
            }
        }
        
        calculate_thruster_pwm(base_thrust, turn_thrust, depth_pid_output, 
                             &controller->current_state.thruster_pwm);
        
        controller->last_depth_error = controller->depth_error;
        
        printf("=== circle search mode ===\n");
        printf("state: %s, leg: %d, angle: %.1f°, depth: %.2fm\n", 
               (controller->search_state == SEARCH_FORWARD) ? "long forward" :
               (controller->search_state == SEARCH_SHORT_FORWARD) ? "short forward" :
               (controller->search_state == SEARCH_STABILIZING) ? "stabilizing" :
               (controller->search_state == SEARCH_TURNING_LEFT) ? "left turn" : 
               (controller->search_state == SEARCH_TURNING_RIGHT) ? "right turn" : "complete",
               controller->current_leg + 1, current_angle, current_depth);
    }
    
    *state = controller->current_state;
    return AUV_SUCCESS;
}

AUV_CIRCLE_API AUVError auv_controller_start_circle(AUVControllerHandle handle, double initial_angle) {
    if (!handle) {
        return AUV_ERROR_INVALID_PARAM;
    }
    
    AUVController* controller = static_cast<AUVController*>(handle);
    if (!controller->initialized) {
        return AUV_ERROR_NOT_INITIALIZED;
    }
    
    controller->is_active = true;
    controller->start_time = get_current_time();
    controller->elapsed_time = 0.0;
    controller->initial_angle = initial_angle;  // record initial angle
    controller->angle_integral = 0.0;
    controller->last_angle_error = 0.0;
    controller->depth_integral = 0.0;
    controller->last_depth_error = 0.0;
    
    controller->reference_angle = initial_angle;  // 记录参考角度
    controller->expected_total_turn = 0.0;        // 重置预期转向
    
    // 重置搜索状态
    controller->search_state = SEARCH_FORWARD;
    controller->current_leg = 0;
    controller->leg_start_time = 0.0;
    controller->cycle_turn_count = 0;
    controller->current_cycle_turn_left = true;
    
    printf("start circle from reference angle: %.2f\n", initial_angle);
    return AUV_SUCCESS;
}

AUV_CIRCLE_API AUVError auv_controller_stop(AUVControllerHandle handle) {
    if (!handle) {
        return AUV_ERROR_INVALID_PARAM;
    }
    
    AUVController* controller = static_cast<AUVController*>(handle);
    if (!controller->initialized) {
        return AUV_ERROR_NOT_INITIALIZED;
    }
    
    controller->is_active = false;
    controller->current_state.motion_mode = MOTION_STOP;
    memset(&controller->current_state.thruster_pwm, 0, sizeof(ThrusterPWM));
    controller->current_state.is_active = 0;
    
    return AUV_SUCCESS;
}

AUV_CIRCLE_API int auv_controller_is_active(AUVControllerHandle handle) {
    if (!handle) {
        return 0;
    }
    
    AUVController* controller = static_cast<AUVController*>(handle);
    if (!controller->initialized) {
        return 0;
    }
    
    return controller->is_active ? 1 : 0;
}

AUV_CIRCLE_API AUVError auv_controller_set_params(
    AUVControllerHandle handle,
    const ControlParams* params
) {
    if (!handle || !params) {
        return AUV_ERROR_INVALID_PARAM;
    }
    
    if (params->radius <= 0 || params->target_speed <= 0 || 
        params->max_angular_velocity <= 0) {
        return AUV_ERROR_INVALID_PARAM;
    }
    
    AUVController* controller = static_cast<AUVController*>(handle);
    if (!controller->initialized) {
        return AUV_ERROR_NOT_INITIALIZED;
    }
    
    controller->params = *params;
    return AUV_SUCCESS;
}

AUV_CIRCLE_API AUVError auv_controller_get_state(
    AUVControllerHandle handle,
    MotionState* state
) {
    if (!handle || !state) {
        return AUV_ERROR_INVALID_PARAM;
    }
    
    AUVController* controller = static_cast<AUVController*>(handle);
    if (!controller->initialized) {
        return AUV_ERROR_NOT_INITIALIZED;
    }
    
    *state = controller->current_state;
    return AUV_SUCCESS;
}

AUV_CIRCLE_API const char* auv_controller_get_error_string(AUVError error_code) {
    switch (error_code) {
        case AUV_SUCCESS:
            return "Success";
        case AUV_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case AUV_ERROR_NOT_INITIALIZED:
            return "Not initialized";
        case AUV_ERROR_UNKNOWN:
        default:
            return "Unknown error";
    }
}










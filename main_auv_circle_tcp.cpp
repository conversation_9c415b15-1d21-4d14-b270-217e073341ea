#include <stdio.h>
#include <cmath>
#include <winsock2.h>
#include <windows.h>
#include <signal.h>
#include "auv_circle_control.h"
#include <conio.h>

#pragma comment(lib, "ws2_32.lib")

volatile int keep_running = 1;
BOOL WINAPI console_handler(DWORD signal) {
    if (signal == CTRL_C_EVENT) {
        printf("\n收到Ctrl+C，程序即将安全退出...\n");
        keep_running = 0;
        return TRUE;
    }
    return FALSE;
}

// 水压(mBar)转深度(m)
double pressure_to_depth(double water_pressure, double p0 = 1013.0, double rho = 1000.0, double g = 9.8) {
    return (water_pressure-p0) * 100.0 / (rho * g);
}

// 解析传感器数据格式1
bool parse_sensor_data(const char* buf, double* yaw, double* depth) {
    int v_adc, i_adc, air_pressure, humidity, water_pressure, tds;
    float roll, pitch, fyaw, temp, water_temp;
    int n = sscanf(buf, "[%d,%d,%f,%f,%f,%d,%f,%d,%d,%f,%d]",
        &v_adc, &i_adc, &roll, &pitch, &fyaw, &air_pressure, &temp, &humidity, &water_pressure, &water_temp, &tds);
    if (n < 11) return false;
    *yaw = (double)fyaw;
    *depth = pressure_to_depth((double)water_pressure); // 用转换公式
    printf("原始数据: %s\n", buf);
    return true;
}

int main() {
    SetConsoleCtrlHandler(console_handler, TRUE);
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2,2), &wsaData);
    SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
    sockaddr_in serv_addr;
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(5000);
    serv_addr.sin_addr.s_addr = inet_addr("192.168.137.99");
    if (connect(sock, (sockaddr*)&serv_addr, sizeof(serv_addr)) < 0) {
        printf("连接服务器失败！(Connect to server failed!)\n");
        return -1;
    }
    printf("已连接到数据流服务器。\n");

    // 设置socket为非阻塞
    u_long mode = 1;
    ioctlsocket(sock, FIONBIO, &mode);

    int base_pwm = 1400;      // 1600为前进
    int diff_pwm = 40;       // 100为适中差速
    char recv_buf[256];
    char send_buf[256];
    int step = 0;
    while (keep_running && step < 1200) {
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(sock, &readfds);
        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 10000; // 10ms

        int ret = select(0, &readfds, NULL, NULL, &tv);
        if (ret > 0 && FD_ISSET(sock, &readfds)) {
            int len = recv(sock, recv_buf, sizeof(recv_buf)-1, 0);
            if (len > 0) {
                recv_buf[len] = 0;
                // 直接写死PWM，不用动态库
                snprintf(send_buf, sizeof(send_buf),
                    "[%d,%d,%d,%d,%d,%d,%d,%d,1500,1500,1500,1500]",
                    base_pwm - diff_pwm,  // L1
                    base_pwm - diff_pwm,  // L2
                    base_pwm + diff_pwm,  // R1
                    base_pwm + diff_pwm,  // R2
                    1500, 1500, 1500, 1500 // 垂直推进器
                );
                printf("Step %d | 固定差速圆周运动 | PWM: %s\n", step, send_buf);
                send(sock, send_buf, strlen(send_buf), 0);
                ++step;
            }
        }
        if (_kbhit()) {
            int ch = _getch();
            if (ch == 'q' || ch == 'Q') {
                printf("检测到按键Q，程序即将退出...\n");
                break;
            }
        }
        // 每10ms检查一次Ctrl+C
    }
    closesocket(sock);
    WSACleanup();
    printf("主循环结束，程序已安全退出。\n");
    return 0;
} 
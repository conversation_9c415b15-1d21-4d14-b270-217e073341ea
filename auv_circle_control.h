/**
 * AUV圆周运动控制库 
 * AUV围绕水池做圆周运动，运动半径为1m
 * 日期: 2025.7.21
 */

#ifndef AUV_CIRCLE_CONTROL_H
#define AUV_CIRCLE_CONTROL_H

#ifdef _WIN32
  #ifdef AUV_CIRCLE_CONTROL_EXPORTS
    #define AUV_CIRCLE_API __declspec(dllexport)
  #else
    #define AUV_CIRCLE_API __declspec(dllimport)
  #endif
#else
  #define AUV_CIRCLE_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 错误码
typedef enum {
    AUV_SUCCESS = 0,
    AUV_ERROR_INVALID_PARAM = -1,
    AUV_ERROR_NOT_INITIALIZED = -2,
    AUV_ERROR_UNKNOWN = -99
} AUVError;

// 运动模式
typedef enum {
    MOTION_STOP = 0,
    MOTION_CIRCLE = 1
} MotionMode;

// 8推进器PWM结构体
typedef struct {
    int horizontal_front_left;
    int horizontal_front_right;
    int horizontal_back_left;
    int horizontal_back_right;
    int vertical_front_left;
    int vertical_front_right;
    int vertical_back_left;
    int vertical_back_right;
} ThrusterPWM;

// 控制参数
typedef struct {
    double radius;                // 圆周半径(m)
    double target_speed;          // 目标线速度(m/s)
    double max_angular_velocity;  // 最大角速度(rad/s)
    double target_depth;          // 目标深度(m)
    double depth_tolerance;       // 深度容差(m)
    double kp, ki, kd;            // 水平PID参数
    double kp_depth, ki_depth, kd_depth; // 垂直PID参数
} ControlParams;

// 运动状态
typedef struct {
    double angle;                 // 当前角度(rad)
    double angular_velocity;      // 当前角速度(rad/s)
    double linear_velocity;       // 当前线速度(m/s)
    double depth_velocity;        // 当前深度变化速度
    MotionMode motion_mode;       // 当前运动模式
    int is_active;                // 是否激活
    ThrusterPWM thruster_pwm;     // 推进器PWM
} MotionState;

// 控制器句柄
typedef void* AUVControllerHandle;

// API声明
AUV_CIRCLE_API AUVControllerHandle auv_controller_create(void);
AUV_CIRCLE_API void auv_controller_destroy(AUVControllerHandle handle);
AUV_CIRCLE_API AUVError auv_controller_init(AUVControllerHandle handle, const ControlParams* params);
AUV_CIRCLE_API AUVError auv_controller_update(
    AUVControllerHandle handle,
    double current_angle,
    double current_angular_velocity,
    double current_linear_velocity,
    double current_depth,
    MotionState* state
);
AUV_CIRCLE_API AUVError auv_controller_start_circle(AUVControllerHandle handle);
AUV_CIRCLE_API AUVError auv_controller_stop(AUVControllerHandle handle);
AUV_CIRCLE_API int auv_controller_is_active(AUVControllerHandle handle);
AUV_CIRCLE_API AUVError auv_controller_set_params(AUVControllerHandle handle, const ControlParams* params);
AUV_CIRCLE_API AUVError auv_controller_get_state(AUVControllerHandle handle, MotionState* state);
AUV_CIRCLE_API const char* auv_controller_get_error_string(AUVError error_code);

#ifdef __cplusplus
}
#endif

#endif // AUV_CIRCLE_CONTROL_H 